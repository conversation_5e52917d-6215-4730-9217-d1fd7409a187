"use client"

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Spinner } from '@/components/ui/shadcn-io/spinner'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb'
import { useBrands, useLanguages, useCreateNewsletter } from '@/hooks'
import { useTemplates } from '@/hooks/use-templates'
import { FolderSelector } from '@/components/salesforce/folder-selector'
import { SalesforceFolder } from '@/types/salesforce'
import { NextPage } from 'next'
import { useEffect, useState, useCallback, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { Check, Building2, FileText, Languages, Home, Palette } from 'lucide-react'
import { toast } from 'sonner'

interface Props { }

const Page: NextPage<Props> = ({ }) => {
  const router = useRouter()
  const { brands, loading: brandsLoading } = useBrands()
  const { templates, loading: templatesLoading } = useTemplates()
  const { languages, loading: languagesLoading } = useLanguages()
  const { createNewsletter, loading: createLoading, error: createError } = useCreateNewsletter()

  const [brand, setBrand] = useState<string>("")
  const [formData, setFormData] = useState<{
    name: string
    template: string
    languages: string[]
  }>({
    name: '',
    template: '',
    languages: []
  })
  const [selectedFolder, setSelectedFolder] = useState<SalesforceFolder | null>(null)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [createdNewsletterId, setCreatedNewsletterId] = useState<string | null>(null)

  // Initialize languages when they load
  useEffect(() => {
    if (languages.length > 0 && formData.languages.length === 0) {
      setFormData(prev => ({
        ...prev,
        languages: languages.map(lang => lang.language)
      }))
    }
  }, [languages, formData.languages.length])

  useEffect(() => {
    // Reset template selection when brand changes
    setFormData(prev => ({
      ...prev,
      template: ''
    }))
  }, [brand])

  const getSelectedBrand = () => brands.find(b => b.id === brand)
  const getSelectedTemplate = () => templates.find(t => t.id === formData.template)
  const filteredTemplates = templates.filter(template => template.brand === brand)

  const canSubmit = brand && formData.template && formData.name.trim() && formData.languages.length > 0
  const isLoading = isSubmitting || createLoading

  // Memoized onChange handler for newsletter name to prevent re-renders
  const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value.substring(0, 100)
    setFormData(prev => ({ ...prev, name: newValue }))
  }, [])

  const handleSubmit = async () => {
    if (!canSubmit) return

    setIsSubmitting(true)
    try {
      const newsletterData = {
        name: formData.name,
        template: formData.template,
        languages: formData.languages,
        ...(selectedFolder && { salesforce_folder: selectedFolder.id })
      }

      console.log('Creating newsletter:', newsletterData)
      const result = await createNewsletter(newsletterData)

      console.log('Newsletter created successfully:', result)
      setCreatedNewsletterId(result.id)

      // Show success toast
      toast.success('Newsletter creada amb èxit!', {
        description: `La newsletter "${formData.name}" s'ha creat correctament.`
      })

      setShowSuccessDialog(true)
    } catch (error) {
      console.error('Error creating newsletter:', error)

      // Show error toast
      const errorMessage = error instanceof Error ? error.message : 'Error desconegut al crear la newsletter'
      toast.error('Error al crear la newsletter', {
        description: errorMessage
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const BrandSelection = useMemo(() => {
    if (brandsLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <Spinner key="infinite" variant="infinite" size={64} />
        </div>
      )
    }

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Selecciona la marca
          </CardTitle>
          <CardDescription>
            Escull la marca per a la qual vols crear la newsletter
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {brands.map((brandOption) => (
              <Card
                key={brandOption.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  brand === brandOption.id ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/50'
                }`}
                onClick={() => setBrand(brandOption.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{brandOption.name}</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Marca corporativa
                      </p>
                    </div>
                    {brand === brandOption.id && (
                      <Check className="h-5 w-5 text-primary" />
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }, [brandsLoading, brands, brand])

  const TemplateSelection = useMemo(() => {
    if (templatesLoading) {
      return (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center py-12">
              <Spinner key="infinite" variant="infinite" size={64} />
            </div>
          </CardContent>
        </Card>
      )
    }

    if (!brand) {
      return (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-muted-foreground" />
              Selecciona la plantilla
            </CardTitle>
            <CardDescription>
              Primer selecciona una marca per veure les plantilles disponibles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-center py-8">
              Selecciona una marca per continuar
            </p>
          </CardContent>
        </Card>
      )
    }

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Selecciona la plantilla
          </CardTitle>
          <CardDescription>
            Escull una plantilla per a la marca {getSelectedBrand()?.name}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredTemplates.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">
              No hi ha plantilles disponibles per aquesta marca.
            </p>
          ) : (
            <div className="overflow-hidden rounded-lg border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nom</TableHead>
                    <TableHead>Descripció</TableHead>
                    <TableHead className="w-[100px]">Estat</TableHead>
                    <TableHead className="w-[80px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTemplates.map((template) => (
                    <TableRow
                      key={template.id}
                      className={`cursor-pointer transition-colors ${
                        formData.template === template.id ? 'bg-primary/5' : 'hover:bg-muted/50'
                      }`}
                      onClick={() => setFormData(prev => ({ ...prev, template: template.id }))}
                    >
                      <TableCell className="font-medium">{template.name}</TableCell>
                      <TableCell className="text-muted-foreground">
                        {template.description || 'Sense descripció'}
                      </TableCell>
                      <TableCell>
                        <Badge variant={template.is_active ? "default" : "secondary"}>
                          {template.is_active ? "Activa" : "Inactiva"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {formData.template === template.id && (
                          <Check className="h-4 w-4 text-primary" />
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }, [templatesLoading, brand, filteredTemplates, formData.template, getSelectedBrand])

  const ConfigurationSection = useMemo(() => {
    if (languagesLoading) {
      return (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center py-12">
              <Spinner key="infinite" variant="infinite" size={64} />
            </div>
          </CardContent>
        </Card>
      )
    }

    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Newsletter Name */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Nom de la Newsletter
            </CardTitle>
            <CardDescription>
              Introdueix un nom descriptiu per a aquesta newsletter
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="newsletter-name">Nom *</Label>
              <Input
                key="newsletter-name-input"
                id="newsletter-name"
                placeholder="Ex: Newsletter Setmanal Gener 2024"
                value={formData.name}
                onChange={handleNameChange}
                maxLength={100}
              />
              <p className="text-xs text-muted-foreground">
                {formData.name.length}/100 caràcters
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Languages Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Languages className="h-5 w-5" />
              Idiomes
            </CardTitle>
            <CardDescription>
              Selecciona els idiomes en què es publicarà la newsletter (tots seleccionats per defecte)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {languages.map((language) => (
                <div key={language.language} className="flex items-center space-x-3">
                  <Checkbox
                    id={`lang-${language.language}`}
                    checked={formData.languages.includes(language.language)}
                    onCheckedChange={(checked) => {
                      setFormData(prev => ({
                        ...prev,
                        languages: checked
                          ? [...prev.languages, language.language]
                          : prev.languages.filter(l => l !== language.language)
                      }))
                    }}
                  />
                  <Label
                    htmlFor={`lang-${language.language}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {language.language_display} ({language.language.toUpperCase()})
                  </Label>
                </div>
              ))}
              {formData.languages.length === 0 && (
                <p className="text-xs text-muted-foreground">
                  Selecciona almenys un idioma
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Salesforce Folder Selection */}
        <FolderSelector
          selectedFolderId={selectedFolder?.id}
          onFolderSelect={setSelectedFolder}
        />
      </div>
    )
  }, [languagesLoading, languages, formData.name, formData.languages, handleNameChange])

  return (
    <>
      <div className="p-6 space-y-8">
        <div className="">
          {/* Header */}
          <div className="mb-8">

            <div className="">
              <h1 className="text-3xl font-bold tracking-tight">Crear Newsletter</h1>
              <p className="text-muted-foreground mt-2">
                Completa la informació per crear una nova newsletter
              </p>
            </div>
          </div>

          {/* Progress Summary */}
          {(brand || formData.template || formData.languages.length > 0 || formData.name || selectedFolder) && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-base">Resum de la selecció</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-4 text-sm">
                  {brand && (
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Marca</Badge>
                      <span>{getSelectedBrand()?.name}</span>
                    </div>
                  )}
                  {formData.template && (
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Plantilla</Badge>
                      <span>{getSelectedTemplate()?.name}</span>
                    </div>
                  )}
                  {formData.languages.length > 0 && (
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Idiomes</Badge>
                      <span>{formData.languages.join(", ").toUpperCase()}</span>
                    </div>
                  )}
                  {formData.name && (
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Nom</Badge>
                      <span>{formData.name}</span>
                    </div>
                  )}
                  {selectedFolder && (
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Carpeta Salesforce</Badge>
                      <span>{selectedFolder.name}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Main Content */}
          <div className="space-y-6">
            {BrandSelection}
            {TemplateSelection}
            {ConfigurationSection}
          </div>

          {/* Submit Button */}
          <div className="flex justify-end mt-8">
            <Button
              onClick={handleSubmit}
              disabled={!canSubmit || isLoading}
              size="lg"
            >
              {isLoading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Creant...
                </>
              ) : (
                'Crear Newsletter'
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Check className="h-5 w-5 text-green-600" />
              Newsletter creada amb èxit!
            </DialogTitle>
            <DialogDescription>
              La newsletter "{formData.name}" s'ha creat correctament. Què vols fer ara?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setShowSuccessDialog(false)
                router.push('/')
              }}
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Anar a l'inici
            </Button>
            <Button
              onClick={() => {
                setShowSuccessDialog(false)
                if (createdNewsletterId) {
                  router.push(`/newsletter/builder/${createdNewsletterId}`)
                } else {
                  router.push('/newsletter/builder')
                }
              }}
              className="flex items-center gap-2"
            >
              <Palette className="h-4 w-4" />
              Anar al constructor
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default Page